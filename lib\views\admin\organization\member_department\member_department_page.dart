import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/components/selector/department_selector/department_tree.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/views/admin/organization/member_department/create_department_dialog.dart';

class MemberDepartmentPage extends StatefulWidget {
  const MemberDepartmentPage({super.key});

  @override
  State<MemberDepartmentPage> createState() => _MemberDepartmentPageState();
}

class _MemberDepartmentPageState extends State<MemberDepartmentPage> {
  // 创建 GlobalKey 用于访问 DepartmentSelector 的方法
  final GlobalKey<DepartmentTreeState> _departmentSelectorKey = GlobalKey<DepartmentTreeState>();

  // 当前选中的部门（高亮显示）
  DepartmentModel? _selectedDepartment;

  // 当前复选框选中的部门列表
  List<DepartmentModel> _checkedDepartments = [];

  /// 更新选中的部门列表
  void _updateCheckedDepartments() {
    final checkedDepartments =
        _departmentSelectorKey.currentState?.getAllCheckedDepartments() ?? [];
    setState(() {
      _checkedDepartments = checkedDepartments;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 左侧部门选择器
        SizedBox(
          width: 250,
          child: DepartmentTree(
            key: _departmentSelectorKey,
            onNodeTap: (department) {
              // 处理部门节点点击事件（高亮显示）
              setState(() {
                _selectedDepartment = department;
              });
              debugPrint('高亮选择了部门: ${department.departmentName}');
            },
          ),
        ),

        // 右侧操作区域
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部操作栏
                Row(
                  children: [
                    CreateDepartmentDrawer(
                      onSuccess: () {
                        _departmentSelectorKey.currentState?.refresh();
                      },
                    ),
                    const SizedBox(width: 16),
                  ],
                ),

                const SizedBox(height: 16),

                // 显示当前高亮选择的部门
                if (_selectedDepartment != null) ...[
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('当前选择的部门（高亮）', style: Theme.of(context).textTheme.titleMedium),
                          const SizedBox(height: 8),
                          Text('部门名称: ${_selectedDepartment!.departmentName}'),
                          if (_selectedDepartment!.description.isNotEmpty)
                            Text('部门描述: ${_selectedDepartment!.description}'),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // 显示复选框选中的部门列表
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '复选框选中的部门 (${_checkedDepartments.length}个)',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        if (_checkedDepartments.isEmpty)
                          const Text('暂无选中的部门')
                        else
                          ...(_checkedDepartments.map(
                            (dept) => Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2.0),
                              child: Text('• ${dept.departmentName}'),
                            ),
                          )),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
