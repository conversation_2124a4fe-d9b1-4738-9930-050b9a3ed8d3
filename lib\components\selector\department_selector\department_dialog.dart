import 'package:flutter/material.dart';
import 'package:octasync_client/components/selector/department_selector/department_tree.dart';
import 'package:octasync_client/imports.dart';

class DepartmentDialog extends StatefulWidget {
  const DepartmentDialog({super.key});

  @override
  State<DepartmentDialog> createState() => _DepartmentDialogState();
}

class _DepartmentDialogState extends State<DepartmentDialog> {
  // 创建 GlobalKey 用于访问 DepartmentSelector 的方法
  final GlobalKey<DepartmentTreeState> _departmentSelectorKey = GlobalKey<DepartmentTreeState>();

  /// 打开选择部门弹窗
  void _showDepSelectorDialog(BuildContext context) {
    AppDialog.show(
      width: 800,
      height: 600,
      context: context,
      title: '选择部门',
      isDrawer: false,
      child: SizedBox(
        width: 700,
        height: 600,
        child: Row(
          children: [
            DepartmentTree(
              key: _departmentSelectorKey,
              onNodeTap: (department) {
                debugPrint('高亮选择了部门: ${department.departmentName}');
              },
            ),
            Container(color: Colors.amber),
          ],
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AppButton(
      text: '选择上级部门',
      type: ButtonType.primary,
      size: ButtonSize.small,
      onPressed: () => _showDepSelectorDialog(context),
    );
  }
}
